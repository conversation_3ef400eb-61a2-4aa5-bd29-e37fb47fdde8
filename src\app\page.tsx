import AboutSection from './components/AboutSection'
import ProjectsSection from './components/ProjectsSection'
import DataVisualization from './components/DataVisualization'
import TestimonialsSection from './components/TestimonialsSection'
import ContactSection from './components/ContactSection'
'use client'

import { useEffect, useState } from 'react'

export default function Home() {
  const [displayText, setDisplayText] = useState('')
  const [currentSkill, setCurrentSkill] = useState(0)
  const fullText = '<PERSON> • AI/ML Engineer'
  const skills = ['NLP', 'Computer Vision', 'Recommender Systems']

  useEffect(() => {
    let charIndex = 0
    const typingInterval = setInterval(() => {
      setDisplayText(fullText.slice(0, charIndex))
      charIndex++
      if (charIndex > fullText.length) clearInterval(typingInterval)
    }, 100)
    
    return () => clearInterval(typingInterval)
  }, [])

  useEffect(() => {
    const rotateInterval = setInterval(() => {
      setCurrentSkill(prev => (prev + 1) % skills.length)
    }, 3000)
    
    return () => clearInterval(rotateInterval)
  }, [])

  return (
    <>
      <main className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-oxford-blue to-navy-blue p-4">
        <div className="text-center max-w-3xl">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
            {displayText}
            <span className="ml-2 animate-pulse">|</span>
          </h1>
          
          <p className="text-xl md:text-2xl text-gray-200 mb-8">
            Specializing in <span className="font-semibold text-white animate-fadeIn">{skills[currentSkill]}</span>
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-federal-blue hover:bg-navy-blue text-white font-bold py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105">
              View Projects
            </button>
            <button className="bg-white hover:bg-gray-100 text-oxford-blue font-bold py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105">
              Download Résumé
            </button>
          </div>
        </div>
        
        <div className="absolute bottom-8 animate-bounce">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </main>
      
      <AboutSection />
      <ProjectsSection />
      <DataVisualization />
      <TestimonialsSection />
      <ContactSection />
    </>
  )
}
