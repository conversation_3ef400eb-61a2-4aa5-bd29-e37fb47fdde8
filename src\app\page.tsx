'use client'

import AboutSection from './components/AboutSection'
import ProjectsSection from './components/ProjectsSection'
import DataVisualization from './components/DataVisualization'
import TestimonialsSection from './components/TestimonialsSection'
import ContactSection from './components/ContactSection'
import { useEffect, useState } from 'react'

export default function Home() {
  const [displayText, setDisplayText] = useState('')
  const [currentSkill, setCurrentSkill] = useState(0)
  const fullText = '<PERSON> • AI/ML Engineer'
  const skills = ['NLP', 'Computer Vision', 'Recommender Systems']

  useEffect(() => {
    let charIndex = 0
    const typingInterval = setInterval(() => {
      setDisplayText(fullText.slice(0, charIndex))
      charIndex++
      if (charIndex > fullText.length) clearInterval(typingInterval)
    }, 100)

    return () => clearInterval(typingInterval)
  }, [])

  useEffect(() => {
    const rotateInterval = setInterval(() => {
      setCurrentSkill(prev => (prev + 1) % skills.length)
    }, 3000)

    return () => clearInterval(rotateInterval)
  }, [])

  return (
    <>
      <main id="home" className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-purple-900 via-indigo-900 to-black p-4 relative overflow-hidden">
        {/* Background animated elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-indigo-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>

        <div className="text-center max-w-3xl relative z-10">
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-4 relative">
            <span className="relative inline-block text-white glow-text">
              {displayText}
              <span className="ml-2 animate-pulse">|</span>
            </span>
          </h1>

          <p className="text-xl md:text-2xl lg:text-3xl text-gray-200 mb-8 relative">
            Specializing in <span className="font-semibold text-white glow-text-small animate-fadeIn">{skills[currentSkill]}</span>
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-bold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-purple-500/25 border border-purple-400/30">
              View Projects
            </button>
            <button className="bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white font-bold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg border border-white/30 hover:border-white/50">
              Download Résumé
            </button>
          </div>
        </div>

        <div className="absolute bottom-8 animate-bounce">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </main>

      <AboutSection />
      <ProjectsSection />
      <DataVisualization />
      <TestimonialsSection />
      <ContactSection />
    </>
  )
}
