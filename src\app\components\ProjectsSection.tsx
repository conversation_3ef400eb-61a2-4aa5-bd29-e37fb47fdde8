'use client';

import { useState } from 'react';
import { FaGithub, FaExternalLinkAlt } from 'react-icons/fa';

const categories = ['All', 'NLP', 'Computer Vision', 'Time Series', 'Recommender Systems'];

const projects = [
  {
    id: 1,
    title: 'Multilingual Sentiment Analysis',
    description: 'Transformer-based model for sentiment analysis in 10+ languages with 92% accuracy',
    category: 'NLP',
    tags: ['BERT', 'PyTorch', 'Hugging Face'],
  },
  {
    id: 2,
    title: 'Real-Time Object Detection',
    description: 'YOLOv5 implementation for real-time object detection in video streams',
    category: 'Computer Vision',
    tags: ['YOLOv5', 'OpenCV', 'TensorRT'],
  },
  {
    id: 3,
    title: 'Stock Price Forecasting',
    description: 'LSTM network for predicting stock prices with 85% directional accuracy',
    category: 'Time Series',
    tags: ['LSTM', 'TensorFlow', 'Pandas'],
  },
  {
    id: 4,
    title: 'E-commerce Recommender',
    description: 'Hybrid recommender system combining collaborative and content-based filtering',
    category: 'Recommender Systems',
    tags: ['Matrix Factorization', 'Scikit-Learn', 'Flask'],
  },
];

export default function ProjectsSection() {
  const [activeCategory, setActiveCategory] = useState('All');

  const filteredProjects = activeCategory === 'All'
    ? projects
    : projects.filter(project => project.category === activeCategory);

  return (
    <section id="projects" className="py-20 bg-gradient-to-br from-white via-indigo-50/30 to-purple-50/30 dark:from-gray-900 dark:via-indigo-900/20 dark:to-purple-900/20">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold glass-text-small text-oxford-blue dark:text-white inline-block">
            Projects Showcase
          </h2>
        </div>

        {/* Category Filters */}
        <div className="flex flex-wrap justify-center gap-3 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={`px-6 py-3 rounded-full transition-all duration-300 font-medium ${
                activeCategory === category
                  ? 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-lg scale-105'
                  : 'glass-button text-purple-600 dark:text-purple-300 hover:scale-105'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map((project) => (
            <div
              key={project.id}
              className="glass rounded-xl overflow-hidden shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-2xl"
              data-category={project.category}
            >
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-xl font-bold text-oxford-blue dark:text-white">
                    {project.title}
                  </h3>
                  <span className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white text-xs px-3 py-1 rounded-full shadow-md">
                    {project.category}
                  </span>
                </div>

                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {project.description}
                </p>

                <div className="flex flex-wrap gap-2 mb-6">
                  {project.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 text-xs px-2 py-1 rounded"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                <div className="flex justify-between gap-3">
                  <button className="flex items-center glass-button text-purple-600 dark:text-purple-400 font-medium px-4 py-2 rounded-full hover:scale-105 transition-all duration-300">
                    <FaGithub className="mr-2" /> GitHub
                  </button>
                  <button className="flex items-center glass-button text-purple-600 dark:text-purple-400 font-medium px-4 py-2 rounded-full hover:scale-105 transition-all duration-300">
                    <FaExternalLinkAlt className="mr-2" /> Demo
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}