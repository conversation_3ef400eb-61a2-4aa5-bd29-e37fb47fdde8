'use client';

import { useState, ChangeEvent, FormEvent } from 'react';
import { FaPaperPlane, FaCheck, FaEnvelope, FaLinkedin, FaGithub, FaTwitter } from 'react-icons/fa';
import StarIcon from './StarIcon';

interface FormData {
  name: string;
  email: string;
  message: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  message?: string;
}

export default function ContactSection() {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    message: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const validate = (): FormErrors => {
    const newErrors: FormErrors = {};
    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }
    if (!formData.message.trim()) newErrors.message = 'Message is required';
    return newErrors;
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const validationErrors = validate();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);

    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSuccess(true);
      setFormData({ name: '', email: '', message: '' });

      // Reset success message after 5 seconds
      setTimeout(() => setIsSuccess(false), 5000);
    }, 1500);
  };

  return (
    <section id="contact" className="relative py-20 bg-gradient-to-br from-gray-100 via-[#9999ff]/10 to-gray-50 dark:from-gray-800 dark:via-[#9999ff]/20 dark:to-black">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-oxford-blue dark:text-white">
            Get In Touch
          </h2>
        </div>

        <div className="flex flex-col lg:flex-row gap-12">
          {/* Contact Form */}
          <div className="lg:w-2/3">
            <div className="sticky top-24 glass rounded-2xl shadow-xl p-8">
              {isSuccess ? (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-6">
                    <FaCheck className="text-3xl text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="text-2xl font-bold text-oxford-blue dark:text-white mb-2">
                    Message Sent!
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Thanks for reaching out. I'll get back to you soon.
                  </p>
                </div>
              ) : (
                <>
                  <h3 className="text-xl font-bold text-oxford-blue dark:text-white mb-6">
                    Send me a message
                  </h3>

                  <form onSubmit={handleSubmit}>
                    <div className="mb-6">
                      <div className="relative">
                        <input
                          type="text"
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          className={`w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 rounded-lg border ${
                            errors.name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                          } focus:outline-none focus:ring-2 focus:ring-federal-blue dark:focus:ring-blue-500`}
                          placeholder="Your Name"
                        />
                        {errors.name && (
                          <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                        )}
                      </div>
                    </div>

                    <div className="mb-6">
                      <div className="relative">
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          className={`w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 rounded-lg border ${
                            errors.email ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                          } focus:outline-none focus:ring-2 focus:ring-federal-blue dark:focus:ring-blue-500`}
                          placeholder="Your Email"
                        />
                        {errors.email && (
                          <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                        )}
                      </div>
                    </div>

                    <div className="mb-6">
                      <div className="relative">
                        <textarea
                          name="message"
                          value={formData.message}
                          onChange={handleChange}
                          rows={5}
                          className={`w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 rounded-lg border ${
                            errors.message ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                          } focus:outline-none focus:ring-2 focus:ring-federal-blue dark:focus:ring-blue-500`}
                          placeholder="Your Message"
                        ></textarea>
                        {errors.message && (
                          <p className="text-red-500 text-sm mt-1">{errors.message}</p>
                        )}
                      </div>
                    </div>

                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full star-button flex items-center justify-center"
                    >
                      <StarIcon className="star-1" />
                      <StarIcon className="star-2" />
                      <StarIcon className="star-3" />
                      <StarIcon className="star-4" />
                      <StarIcon className="star-5" />
                      <StarIcon className="star-6" />
                      {isSubmitting ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Sending...
                        </>
                      ) : (
                        <>
                          <FaPaperPlane className="mr-2" /> Send Message
                        </>
                      )}
                    </button>
                  </form>
                </>
              )}
            </div>
          </div>

          {/* Contact Info and Social */}
          <div className="lg:w-1/3">
            <div className="glass rounded-2xl shadow-xl p-8 mb-8">
              <h3 className="text-xl font-bold text-oxford-blue dark:text-white mb-6">
                Contact Information
              </h3>

              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="bg-gradient-to-r from-purple-600 to-indigo-600 w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg">
                    <FaEnvelope className="text-xl text-white" />
                  </div>
                  <div className="ml-4">
                    <h4 className="font-semibold text-oxford-blue dark:text-white">Email</h4>
                    <p className="text-gray-600 dark:text-gray-300"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="bg-gradient-to-r from-purple-600 to-indigo-600 w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg">
                    <FaLinkedin className="text-xl text-white" />
                  </div>
                  <div className="ml-4">
                    <h4 className="font-semibold text-oxford-blue dark:text-white">LinkedIn</h4>
                    <p className="text-gray-600 dark:text-gray-300">linkedin.com/in/aimlexpert</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="bg-gradient-to-r from-purple-600 to-indigo-600 w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg">
                    <FaGithub className="text-xl text-white" />
                  </div>
                  <div className="ml-4">
                    <h4 className="font-semibold text-oxford-blue dark:text-white">GitHub</h4>
                    <p className="text-gray-600 dark:text-gray-300">github.com/aimlexpert</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="bg-gradient-to-r from-purple-600 to-indigo-600 w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg">
                    <FaTwitter className="text-xl text-white" />
                  </div>
                  <div className="ml-4">
                    <h4 className="font-semibold text-oxford-blue dark:text-white">Twitter</h4>
                    <p className="text-gray-600 dark:text-gray-300">@aiml_expert</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Newsletter Signup */}
            <div className="glass rounded-2xl shadow-xl p-8">
              <h3 className="text-xl font-bold text-oxford-blue dark:text-white mb-4">
                Stay Updated
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Subscribe to my newsletter for AI insights and project updates.
              </p>

              <form className="space-y-4">
                <div>
                  <input
                    type="email"
                    placeholder="Your email address"
                    className="w-full px-4 py-3 glass rounded-lg border border-white/20 focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  />
                </div>
                <button
                  type="submit"
                  className="w-full star-button flex items-center justify-center"
                >
                  <StarIcon className="star-1" />
                  <StarIcon className="star-2" />
                  <StarIcon className="star-3" />
                  <StarIcon className="star-4" />
                  <StarIcon className="star-5" />
                  <StarIcon className="star-6" />
                  Subscribe
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}