import type { Metadata } from 'next'
import './globals.css'
import AIChatWidget from './components/AIChatWidget'
import FloatingNavbar from './components/FloatingNavbar'

export const metadata: Metadata = {
  title: 'AI/ML Engineer Portfolio',
  description: 'A showcase of AI and machine learning projects',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className="bg-white dark:bg-black text-black dark:text-white">
        <FloatingNavbar />
        {children}
        <AIChatWidget />
      </body>
    </html>
  )
}
