'use client';

import { useState } from 'react';
import { FaPython, FaRobot, FaBrain, FaChartLine } from 'react-icons/fa';

export default function AboutSection() {
  const [expanded, setExpanded] = useState(false);

  const skills = [
    { name: 'Python', level: 95, icon: <FaPython className="text-4xl text-blue-500" /> },
    { name: 'TensorFlow', level: 90, icon: <FaRobot className="text-4xl text-orange-500" /> },
    { name: 'PyTorch', level: 85, icon: <FaBrain className="text-4xl text-red-500" /> },
    { name: 'Scikit-Learn', level: 80, icon: <FaChartLine className="text-4xl text-green-500" /> },
  ];

  return (
    <section id="about" className="py-20 bg-gradient-to-br from-gray-100 via-[#9999ff]/10 to-gray-50 dark:from-gray-800 dark:via-[#9999ff]/20 dark:to-black">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-oxford-blue dark:text-white">
            About Me & Skills
          </h2>
        </div>

        <div className="flex flex-col md:flex-row gap-12">
          {/* Profile Photo */}
          <div className="md:w-1/3 flex justify-center">
            <div className="glass border-2 border-dashed border-purple-300/50 dark:border-purple-400/30 rounded-xl w-64 h-64 flex items-center justify-center text-purple-600 dark:text-purple-300 font-medium">
              Profile Photo
            </div>
          </div>

          {/* Bio and Skills */}
          <div className="md:w-2/3">
            <div className="prose prose-lg dark:prose-invert max-w-none">
              <p className="mb-4">
                I'm an AI/ML Engineer with 5+ years of experience building intelligent systems that solve real-world problems.
                My expertise spans natural language processing, computer vision, and predictive analytics.
              </p>

              {expanded ? (
                <div className="space-y-4">
                  <p>
                    I hold a Master's degree in Computer Science with a specialization in Machine Learning.
                    My research focused on transformer architectures for low-resource languages.
                  </p>
                  <p>
                    I've worked with Fortune 500 companies and startups to implement ML solutions that improved
                    operational efficiency by 40% and increased revenue by 15% through personalized recommendations.
                  </p>
                  <button
                    onClick={() => setExpanded(false)}
                    className="glass-button text-purple-600 dark:text-purple-400 font-medium px-4 py-2 rounded-full hover:scale-105 transition-all duration-300"
                  >
                    Read Less
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setExpanded(true)}
                  className="glass-button text-purple-600 dark:text-purple-400 font-medium px-4 py-2 rounded-full hover:scale-105 transition-all duration-300"
                >
                  Read More
                </button>
              )}
            </div>

            {/* Skills Grid */}
            <div className="mt-10 grid grid-cols-1 sm:grid-cols-2 gap-6">
              {skills.map((skill, index) => (
                <div
                  key={index}
                  className="glass p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  <div className="flex items-center mb-3">
                    {skill.icon}
                    <h3 className="text-xl font-semibold ml-3 text-oxford-blue dark:text-white">
                      {skill.name}
                    </h3>
                  </div>
                  <div className="w-full bg-white/20 dark:bg-black/20 rounded-full h-3 backdrop-blur-sm">
                    <div
                      className="bg-gradient-to-r from-purple-500 to-indigo-500 h-3 rounded-full shadow-lg transition-all duration-1000 ease-out"
                      style={{ width: `${skill.level}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-300 mt-1 block text-right">
                    {skill.level}%
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}