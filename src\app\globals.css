@import "tailwindcss";

html,
body {
  font-family: ui-sans-serif, system-ui, sans-serif;
  scroll-behavior: smooth;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

/* Glassmorphism effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Glassmorphism text effects with subtle glow */
.glass-text {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1rem 2rem;
  text-shadow:
    0 0 3px rgba(255, 255, 255, 0.3),
    0 0 6px rgba(147, 51, 234, 0.2);
  animation: subtle-glow 4s ease-in-out infinite alternate;
}

.glass-text-small {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 0.5rem 1rem;
  text-shadow:
    0 0 2px rgba(255, 255, 255, 0.4),
    0 0 4px rgba(147, 51, 234, 0.2);
  animation: subtle-glow-small 3s ease-in-out infinite alternate;
}

@keyframes subtle-glow {
  0% {
    text-shadow:
      0 0 3px rgba(255, 255, 255, 0.3),
      0 0 6px rgba(147, 51, 234, 0.2);
    box-shadow:
      0 0 20px rgba(255, 255, 255, 0.05),
      inset 0 0 20px rgba(255, 255, 255, 0.05);
  }
  100% {
    text-shadow:
      0 0 5px rgba(255, 255, 255, 0.5),
      0 0 10px rgba(147, 51, 234, 0.3);
    box-shadow:
      0 0 30px rgba(255, 255, 255, 0.1),
      inset 0 0 30px rgba(255, 255, 255, 0.08);
  }
}

@keyframes subtle-glow-small {
  0% {
    text-shadow:
      0 0 2px rgba(255, 255, 255, 0.4),
      0 0 4px rgba(147, 51, 234, 0.2);
    box-shadow:
      0 0 15px rgba(255, 255, 255, 0.03),
      inset 0 0 15px rgba(255, 255, 255, 0.03);
  }
  100% {
    text-shadow:
      0 0 4px rgba(255, 255, 255, 0.6),
      0 0 8px rgba(147, 51, 234, 0.3);
    box-shadow:
      0 0 25px rgba(255, 255, 255, 0.08),
      inset 0 0 25px rgba(255, 255, 255, 0.05);
  }
}

/* Enhanced button styles */
.glass-button {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Star Animation Button Effects */
.star-button {
  position: relative;
  padding: 12px 35px;
  background: #9999ff;
  font-size: 17px;
  font-weight: 500;
  color: #181818;
  border: 3px solid #9999ff;
  border-radius: 50px;
  box-shadow: 0 0 0 #fec1958c;
  transition: all 0.3s ease-in-out;
  cursor: pointer;
  overflow: hidden;
}

.star-1 {
  position: absolute;
  top: 20%;
  left: 20%;
  width: 25px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 1s cubic-bezier(0.05, 0.83, 0.43, 0.96);
}

.star-2 {
  position: absolute;
  top: 45%;
  left: 45%;
  width: 15px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 1s cubic-bezier(0, 0.4, 0, 1.01);
}

.star-3 {
  position: absolute;
  top: 40%;
  left: 40%;
  width: 5px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 1s cubic-bezier(0, 0.4, 0, 1.01);
}

.star-4 {
  position: absolute;
  top: 20%;
  left: 40%;
  width: 8px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 0.8s cubic-bezier(0, 0.4, 0, 1.01);
}

.star-5 {
  position: absolute;
  top: 25%;
  left: 45%;
  width: 15px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 0.6s cubic-bezier(0, 0.4, 0, 1.01);
}

.star-6 {
  position: absolute;
  top: 5%;
  left: 50%;
  width: 5px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 0.8s ease;
}

.star-button:hover {
  background: transparent;
  color: #9999ff;
  box-shadow: 0 0 25px #fec1958c;
}

.star-button:hover .star-1 {
  position: absolute;
  top: -80%;
  left: -30%;
  width: 25px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.star-button:hover .star-2 {
  position: absolute;
  top: -25%;
  left: 10%;
  width: 15px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.star-button:hover .star-3 {
  position: absolute;
  top: 55%;
  left: 25%;
  width: 5px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.star-button:hover .star-4 {
  position: absolute;
  top: 30%;
  left: 80%;
  width: 8px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.star-button:hover .star-5 {
  position: absolute;
  top: 25%;
  left: 115%;
  width: 15px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.star-button:hover .star-6 {
  position: absolute;
  top: 5%;
  left: 60%;
  width: 5px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.fil0 {
  fill: #fffdef;
}

/* Custom color hover effects */
.contact-icon {
  background-color: transparent;
  border: 2px solid #ff0000;
  color: #ff0000;
  transition: all 0.3s ease;
}

.contact-icon:hover {
  border-color: #ff1a8c;
  color: #ff1a8c;
  transform: scale(1.1);
}

.testimonial-avatar {
  background-color: #00cc99;
  transition: all 0.3s ease;
}

.project-filter-active {
  background-color: #00cc99;
  border-color: #00cc99;
}

/* Animated Send Message Button */
.Btn-Container {
  display: flex;
  width: 170px;
  height: fit-content;
  background-color: #1d2129;
  border-radius: 40px;
  box-shadow: 0px 5px 10px #bebebe;
  justify-content: space-between;
  align-items: center;
  border: none;
  cursor: pointer;
}

.icon-Container {
  width: 45px;
  height: 45px;
  background-color: #f59aff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 3px solid #1d2129;
}

.text {
  width: calc(170px - 45px);
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9em;
  letter-spacing: 1.2px;
}

.icon-Container svg {
  transition-duration: 1.5s;
}

.Btn-Container:hover .icon-Container svg {
  transition-duration: 1.5s;
  animation: arrow 1s linear infinite;
}

@keyframes arrow {
  0% {
    opacity: 0;
    margin-left: 0px;
  }
  100% {
    opacity: 1;
    margin-left: 10px;
  }
}
