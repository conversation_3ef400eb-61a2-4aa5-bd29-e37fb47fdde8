@import "tailwindcss";

html,
body {
  font-family: ui-sans-serif, system-ui, sans-serif;
  scroll-behavior: smooth;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

/* Glassmorphism effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Glowing text effects - Moon eclipse style */
.glow-text {
  text-shadow:
    0 0 5px rgba(255, 255, 255, 0.8),
    0 0 10px rgba(255, 255, 255, 0.6),
    0 0 15px rgba(255, 255, 255, 0.4),
    0 0 20px rgba(147, 51, 234, 0.6),
    0 0 35px rgba(147, 51, 234, 0.4),
    0 0 40px rgba(147, 51, 234, 0.3);
  animation: glow-pulse 3s ease-in-out infinite alternate;
}

.glow-text-small {
  text-shadow:
    0 0 3px rgba(255, 255, 255, 0.8),
    0 0 6px rgba(255, 255, 255, 0.6),
    0 0 9px rgba(147, 51, 234, 0.5),
    0 0 12px rgba(147, 51, 234, 0.3);
  animation: glow-pulse-small 2s ease-in-out infinite alternate;
}

@keyframes glow-pulse {
  0% {
    text-shadow:
      0 0 5px rgba(255, 255, 255, 0.8),
      0 0 10px rgba(255, 255, 255, 0.6),
      0 0 15px rgba(255, 255, 255, 0.4),
      0 0 20px rgba(147, 51, 234, 0.6),
      0 0 35px rgba(147, 51, 234, 0.4),
      0 0 40px rgba(147, 51, 234, 0.3);
  }
  100% {
    text-shadow:
      0 0 8px rgba(255, 255, 255, 1),
      0 0 15px rgba(255, 255, 255, 0.8),
      0 0 25px rgba(255, 255, 255, 0.6),
      0 0 30px rgba(147, 51, 234, 0.8),
      0 0 45px rgba(147, 51, 234, 0.6),
      0 0 55px rgba(147, 51, 234, 0.4);
  }
}

@keyframes glow-pulse-small {
  0% {
    text-shadow:
      0 0 3px rgba(255, 255, 255, 0.8),
      0 0 6px rgba(255, 255, 255, 0.6),
      0 0 9px rgba(147, 51, 234, 0.5),
      0 0 12px rgba(147, 51, 234, 0.3);
  }
  100% {
    text-shadow:
      0 0 5px rgba(255, 255, 255, 1),
      0 0 10px rgba(255, 255, 255, 0.8),
      0 0 15px rgba(147, 51, 234, 0.7),
      0 0 20px rgba(147, 51, 234, 0.5);
  }
}
